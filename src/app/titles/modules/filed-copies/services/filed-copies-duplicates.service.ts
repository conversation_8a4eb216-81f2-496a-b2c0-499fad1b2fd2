import { Injectable } from '@angular/core';
import { LinkedDocumentItem } from '../../../types/linked-document-item.type';
import { duplicateColors } from '../constants/duplicate-colors.constant';
import { GroupItem } from '../types/group-item.type';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { SchedulesQuery } from '../../../store';

@Injectable()
export class FiledCopiesDuplicatesService {

    constructor(
        private readonly schedulesQuery: SchedulesQuery,
    ) {
    }

    public selectPossibleDuplicates(): Observable<{ [key: string]: string[] }> {
        return this.schedulesQuery.selectLinkedDocuments()
            .pipe(
                map((schedule) => schedule.possibleDuplicates ?? {}),
            );
    }

    public getPossibleDuplicates(): { [key: string]: string[] } {
        return this.schedulesQuery.getLinkedDocuments().possibleDuplicates ?? {};
    }

    public assignGroupAttributes(items: LinkedDocumentItem[], duplicates: { [key: string]: string[] }): GroupItem<LinkedDocumentItem>[] {
        const groups = this.groupDuplicates(items, 'documentType');
        const result: GroupItem<LinkedDocumentItem>[] = this.assignColorsToDuplicates(groups);

        groups.forEach((groupItems) => {
            if (groupItems.length === 1) {
                result.push(this.assignDefaultAttributes(groupItems[0]));
            }
        });

        return result;
    }

    public assignDefaultAttributes(item: LinkedDocumentItem): GroupItem<LinkedDocumentItem> {
        return {
            ...item,
            bgColor: null,
            borderColor: null,
            isFirst: false,
            isLast: false,
        };
    }

    private groupDuplicates(items: LinkedDocumentItem[], duplicates: { [key: string]: string[] }): Map<string, LinkedDocumentItem[]> {
        const groups = new Map<string, LinkedDocumentItem[]>();

        items.forEach((item) => {
            const reference: string = item[key].toString();

            if (!groups.has(reference)) {
                groups.set(reference, []);
            }

            groups.get(reference).push(item);
        });

        return groups;
    }

    private assignColorsToDuplicates(groups: Map<string, LinkedDocumentItem[]>): GroupItem<LinkedDocumentItem>[] {
        const result: GroupItem<LinkedDocumentItem>[] = [];
        let groupIndex = 0;

        groups.forEach((groupItems) => {
            if (groupItems.length > 1) {
                const groupColors = duplicateColors[groupIndex % duplicateColors.length];
                groupIndex++;

                groupItems.forEach((item, index) => {
                    result.push({
                        ...item,
                        bgColor: groupColors.background,
                        borderColor: groupColors.border,
                        isFirst: index === 0,
                        isLast: index === groupItems.length - 1,
                    });
                });
            }
        });

        return result;
    }
}
